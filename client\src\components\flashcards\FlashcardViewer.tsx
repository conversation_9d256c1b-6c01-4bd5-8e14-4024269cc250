import React, { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Check,
  RefreshCw,
  X,
  Keyboard,
} from "lucide-react";
import { Flashcard as LocalFlashcard } from "@/types"; // Local type (if needed)
import { Flashcard as SupabaseFlashcard } from "@shared/types/flashcards"; // Import from shared types

// Union type to handle both local and Supabase flashcard formats
type FlashcardUnion = LocalFlashcard | SupabaseFlashcard;

// Helper functions to safely access properties from either flashcard type
const getQuestion = (card: FlashcardUnion): string => {
  return (card as SupabaseFlashcard).front_text || (card as LocalFlashcard).question || "No question";
};

const getAnswer = (card: FlashcardUnion): string => {
  return (card as SupabaseFlashcard).back_text || (card as LocalFlashcard).answer || "No answer";
};

const getDeckId = (card: FlashcardUnion): string => {
  return (card as SupabaseFlashcard).set_id || (card as LocalFlashcard).deckId || "";
};

const getCreatedAt = (card: FlashcardUnion): number => {
  const supabaseDate = (card as SupabaseFlashcard).created_at;
  const localDate = (card as LocalFlashcard).createdAt;

  if (supabaseDate) {
    return new Date(supabaseDate).getTime();
  }
  if (localDate) {
    return typeof localDate === 'number' ? localDate : new Date(localDate).getTime();
  }
  return Date.now();
};
import { queryClient } from "@/lib/queryClient";
import { saveFlashcard } from "@/lib/storage";
import { ReviewDifficulty, updateCardWithSpacedRepetition } from "@/lib/srs";
import { useFlashcardNavigation } from "@/hooks/useKeyboardNavigation";


interface FlashcardViewerProps {
  flashcards: FlashcardUnion[];
  deckId?: string; // Optional deckId for saved flashcards
  enableSRS?: boolean; // Flag to enable SRS functionality
}

const FlashcardViewer: React.FC<FlashcardViewerProps> = ({
  flashcards,
  deckId,
  enableSRS = false, // Default to disabled for newly generated cards
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [reviewComplete, setReviewComplete] = useState(false);

  if (!flashcards || flashcards.length === 0) {
    return (
      <p className="text-center text-purple-300 py-10">
        No flashcards to display.
      </p>
    );
  }

  const currentCard = flashcards[currentIndex];

  const handleNext = () => {
    if (currentIndex === flashcards.length - 1) {
      // If SRS is enabled and we've reached the end, mark as complete
      if (enableSRS) {
        setReviewComplete(true);
      } else {
        // For non-SRS mode, just loop back to the first card
        setCurrentIndex(0);
      }
    } else {
      setCurrentIndex(currentIndex + 1);
    }
    setIsFlipped(false);
  };

  const handlePrev = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex - 1 + flashcards.length) % flashcards.length
    );
    setIsFlipped(false);
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  // SRS Mutations
  const reviewMutation = useMutation({
    mutationFn: async (difficulty: ReviewDifficulty) => {
      if (!enableSRS) return currentCard; // If SRS not enabled, do nothing

      // Update the card with the SRS algorithm
      const updatedCard = updateCardWithSpacedRepetition(
        {
          id: currentCard.id,
          question: getQuestion(currentCard),
          answer: getAnswer(currentCard),
          deckId: getDeckId(currentCard),
          createdAt: getCreatedAt(currentCard),
        },
        difficulty
      );

      // If we have a deckId, save to storage
      if (deckId) {
        return saveFlashcard(updatedCard);
      }

      return updatedCard;
    },
    onSuccess: () => {
      // Move to the next card after rating
      handleNext();

      // If we have a deckId, invalidate queries to refresh data
      if (deckId) {
        queryClient.invalidateQueries({
          queryKey: [`/api/flashcard-decks/${deckId}`],
        });
        queryClient.invalidateQueries({
          queryKey: [`/api/flashcard-decks/${deckId}/due`],
        });
        queryClient.invalidateQueries({ queryKey: ["/api/study-stats"] });
      }
    },
  });

  const handleDifficultyRating = (difficulty: ReviewDifficulty) => {
    reviewMutation.mutate(difficulty);
  };

  // Keyboard navigation
  useFlashcardNavigation({
    onFlip: handleFlip,
    onNext: handleNext,
    onPrevious: handlePrev,
    onRateEasy: () =>
      enableSRS && isFlipped && handleDifficultyRating(ReviewDifficulty.EASY),
    onRateMedium: () =>
      enableSRS && isFlipped && handleDifficultyRating(ReviewDifficulty.MEDIUM),
    onRateDifficult: () =>
      enableSRS &&
      isFlipped &&
      handleDifficultyRating(ReviewDifficulty.DIFFICULT),
    disabled: reviewComplete || reviewMutation.isPending,
  });

  // If review is complete, show completion screen
  if (reviewComplete) {
    return (
      <div className="w-full max-w-md mx-auto text-center space-y-4 py-10">
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 flex flex-col items-center shadow-xl">
          <Check className="h-12 w-12 text-green-400 mb-4" />
          <h3 className="text-xl font-medium text-purple-300 mb-2">
            Review Complete!
          </h3>
          <p className="text-slate-300 mb-4">
            You've reviewed all {flashcards.length} cards. The spaced repetition
            system will schedule them for future review.
          </p>
          <Button
            onClick={() => {
              setCurrentIndex(0);
              setIsFlipped(false); // Reset flip state too
              setReviewComplete(false);
            }}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2.5"
          >
            Start Over
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-4">
      <div className="flex justify-between items-center">
        <p className="text-sm text-purple-400">
          Card {currentIndex + 1} of {flashcards.length}
        </p>
        <div className="flex items-center space-x-1 text-xs text-slate-500">
          <Keyboard className="h-3 w-3" />
          <span>Space to flip, ← → to navigate</span>
          {enableSRS && <span>, 1-3 to rate</span>}
        </div>
      </div>

      {/* Show difficulty buttons above the card if SRS is enabled and card is flipped */}
      {enableSRS && isFlipped && (
        <div className="flex justify-between items-center space-x-3 mb-4">
          <Button
            onClick={() => handleDifficultyRating(ReviewDifficulty.DIFFICULT)}
            variant="outline"
            className="border-red-500/70 text-red-400 hover:bg-red-700/20 hover:text-red-300 hover:border-red-500 flex-1 py-2.5"
            disabled={reviewMutation.isPending}
          >
            <X className="mr-1.5 h-4 w-4" /> Difficult
          </Button>
          <Button
            onClick={() => handleDifficultyRating(ReviewDifficulty.MEDIUM)}
            variant="outline"
            className="border-yellow-500/70 text-yellow-400 hover:bg-yellow-700/20 hover:text-yellow-300 hover:border-yellow-500 flex-1 py-2.5"
            disabled={reviewMutation.isPending}
          >
            <RefreshCw className="mr-1.5 h-4 w-4" /> Medium
          </Button>
          <Button
            onClick={() => handleDifficultyRating(ReviewDifficulty.EASY)}
            variant="outline"
            className="border-green-500/70 text-green-400 hover:bg-green-700/20 hover:text-green-300 hover:border-green-500 flex-1 py-2.5"
            disabled={reviewMutation.isPending}
          >
            <Check className="mr-1.5 h-4 w-4" /> Easy
          </Button>
        </div>
      )}

      <Card
        className={`min-h-[250px] md:min-h-[300px] flex flex-col justify-center items-center p-6 cursor-pointer transition-transform duration-700 transform-style-preserve-3d ${
          isFlipped ? "rotate-y-180" : ""
        } bg-slate-800 hover:bg-slate-700 border-slate-700 shadow-xl`}
        onClick={handleFlip}
      >
        <CardContent
          className={`flex items-center justify-center text-center text-slate-100 text-xl md:text-2xl backface-hidden ${
            isFlipped ? "rotate-y-180" : ""
          }`}
        >
          {isFlipped ? getAnswer(currentCard) : getQuestion(currentCard)}
        </CardContent>
      </Card>

      {/* Navigation buttons always at the bottom */}
      <div className="flex justify-between items-center space-x-3">
        <Button
          onClick={handlePrev}
          variant="ghost"
          className="text-purple-300 hover:text-purple-100 hover:bg-slate-700 px-4 py-2.5"
        >
          <ChevronLeft className="mr-1.5 h-5 w-5" /> Prev
        </Button>
        <Button
          onClick={handleFlip}
          variant="outline"
          className="border-purple-500/70 text-purple-300 hover:bg-slate-700 hover:text-purple-100 hover:border-purple-500 flex-grow py-2.5"
        >
          <RotateCcw className="mr-1.5 h-5 w-5" /> Flip Card
        </Button>
        <Button
          onClick={handleNext}
          variant="ghost"
          className="text-purple-300 hover:text-purple-100 hover:bg-slate-700 px-4 py-2.5"
        >
          Next <ChevronRight className="ml-1.5 h-5 w-5" />
        </Button>
      </div>
    </div>
  );
};

export default FlashcardViewer;

// Add these to your global CSS or a relevant CSS file for the flip animation:
/*
.transform-style-preserve-3d {
  transform-style: preserve-3d;
}
.rotate-y-180 {
  transform: rotateY(180deg);
}
.backface-hidden {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden; 
}
*/
